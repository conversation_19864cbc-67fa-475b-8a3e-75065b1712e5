#!/usr/bin/env python3
"""
Script to check available models on the custom endpoint
"""
import asyncio
import aiohttp
import json

async def check_available_models():
    """Check what models are available on the custom endpoint"""
    endpoint = "http://*************:4033/v1"
    
    try:
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            print(f"Checking available models at {endpoint}/models...")
            
            # Try to get models list
            try:
                async with session.get(f"{endpoint}/models") as response:
                    print(f"Status: {response.status}")
                    if response.status == 200:
                        data = await response.json()
                        print(f"Available models:")
                        for model in data.get('data', []):
                            print(f"  - {model.get('id', 'Unknown')}")
                    else:
                        text = await response.text()
                        print(f"Response: {text}")
            except Exception as e:
                print(f"Error getting models: {e}")
            
            # Try a simple test call to see what happens
            print(f"\nTesting a simple API call...")
            headers = {
                'Authorization': 'Bearer sk-YyiBg6DSn1Fc2KBNU6ZYtw',
                'Content-Type': 'application/json'
            }
            
            test_data = {
                "model": "gemma-3-27b",
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }
            
            try:
                async with session.post(f"{endpoint}/chat/completions", 
                                      headers=headers, 
                                      json=test_data) as response:
                    print(f"Test call status: {response.status}")
                    text = await response.text()
                    print(f"Response: {text[:500]}...")
            except Exception as e:
                print(f"Test call error: {e}")
                
    except Exception as e:
        print(f"Connection error: {e}")

if __name__ == "__main__":
    asyncio.run(check_available_models())
