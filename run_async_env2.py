import asyncio
import os
import time
import sys

# Add current directory to path to import the async module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the async function
from env2_box_arrange_async import run_exp_async

Code_dir_path = os.path.dirname(os.path.abspath(__file__)) + '/'  # Current directory path
Saving_path = Code_dir_path + 'Env2_WorkpieceNet2'

# Configuration for model selection
cen_decen_framework = 'HMAS-2'  # Options: 'CMAS', 'DMAS', 'HMAS-1', 'HMAS-2'
local_model_choice = 'gemma-3-27b'  # Options: 'gemma-3-27b', 'qwen3-30b-a3b-mlx'

print(f'-------------------Framework: {cen_decen_framework}, Local Model: {local_model_choice}-------------------')

async def main():
    for pg_row_num, pg_column_num in [(2,2), (2,4), (4,4), (4,8)]:
        if pg_row_num == 4 and pg_column_num == 8:
            query_time_limit = 40
        else:
            query_time_limit = 30
            
        for iteration_num in range(10):
            print('-------###-------###-------###-------')
            print(f'Row num is: {pg_row_num}, Column num is: {pg_column_num}, Iteration num is: {iteration_num}\n\n')
            
            start_time = time.time()
            
            user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result = await run_exp_async(
                Saving_path, pg_row_num, pg_column_num, iteration_num, query_time_limit, 
                dialogue_history_method='_w_only_state_action_history',
                cen_decen_framework=cen_decen_framework, 
                local_model_choice=local_model_choice
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            with open(Saving_path_result + '/token_num_count.txt', 'w') as f:
                for token_num_num_count in token_num_count_list:
                    f.write(str(token_num_num_count) + '\n')

            with open(Saving_path_result + '/success_failure.txt', 'w') as f:
                f.write(success_failure)

            with open(Saving_path_result + '/env_action_times.txt', 'w') as f:
                f.write(f'{index_query_times+1}')
                
            with open(Saving_path_result + '/execution_time.txt', 'w') as f:
                f.write(f'{execution_time:.2f} seconds')
                
            print(success_failure)
            print(f'Iteration number: {index_query_times+1}')
            print(f'Execution time: {execution_time:.2f} seconds')

if __name__ == "__main__":
    asyncio.run(main())
