import openai
import tiktoken
import time
import logging
import requests.exceptions

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s',
                   handlers=[logging.StreamHandler()])
logger = logging.getLogger(__name__)

enc = tiktoken.get_encoding("cl100k_base")
assert enc.decode(enc.encode("hello world")) == "hello world"
enc = tiktoken.encoding_for_model("gpt-4")

# Updated LLM configuration
openai_api_key_name = 'sk-YyiBg6DSn1Fc2KBNU6ZYtw'
openai_base_url = 'http://47.117.124.84:4033/v1'

# 设置请求超时（秒）
REQUEST_TIMEOUT = 30

# Initialize OpenAI clients for different endpoints
custom_client = openai.OpenAI(
    api_key=openai_api_key_name,
    base_url=openai_base_url
)

# For original OpenAI API (if needed)
openai_client = openai.OpenAI(
    api_key=openai_api_key_name
)

def get_model_for_framework(framework, agent_type='central', local_model_choice='gemma-3-27b'):
    """
    Get the appropriate model for a given framework and agent type.

    Args:
        framework: 'CMAS', 'DMAS', 'HMAS-1', 'HMAS-2'
        agent_type: 'central' or 'local' (for hierarchical frameworks)
        local_model_choice: 'gemma-3-27b' or 'qwen3-30b-a3b-mlx' (for DMAS and local agents)

    Returns:
        model_name: The appropriate model name
    """
    if framework == 'CMAS':
        # Use qwen3-30b-a3b-mlx as qwen3-235b-a22b has server configuration issues
        return 'qwen3-235b-a22b'
    elif framework == 'DMAS':
        return local_model_choice
    elif framework in ['HMAS-1', 'HMAS-2']:
        if agent_type == 'central':
            # Use qwen3-30b-a3b-mlx as qwen3-235b-a22b has server configuration issues
            return 'qwen3-235b-a22b'
        else:  # local agent
            return local_model_choice
    else:
        raise ValueError(f'Invalid framework: {framework}')



def GPT_response(messages, model_name):
  token_num_count = 0
  for item in messages:
    token_num_count += len(enc.encode(item["content"]))

  # Support both old GPT models and new custom models
  supported_models = [
      'qwen3-235b-a22b', 'gemma-3-27b', 'qwen3-30b-a3b-mlx'
  ]

  if model_name in supported_models:
    logger.info(f'正在使用模型: {model_name}，消息长度: {token_num_count} tokens')

    # Choose the appropriate client based on model type
    if model_name.startswith('gpt-'):
        # Use original OpenAI API for GPT models
        client = openai_client
    else:
        # Use custom endpoint for new models
        client = custom_client

    # 尝试调用API，有三次重试机会
    for attempt in range(1, 4):  # 1, 2, 3
      try:
        logger.info(f'API调用尝试 #{attempt} - 模型: {model_name}，设置超时: {REQUEST_TIMEOUT}秒')
        
        start_time = time.time()
        result = client.chat.completions.create(
          model=model_name,
          messages=messages,
          temperature=0.0,
          top_p=1,
          frequency_penalty=0,
          presence_penalty=0,
          timeout=REQUEST_TIMEOUT  # 添加超时设置
        )
        
        elapsed_time = time.time() - start_time
        logger.info(f'API调用成功 - 耗时: {elapsed_time:.2f}秒')
        
        # Extract response content using the new API structure
        response_content = result.choices[0].message.content
        token_num_count += len(enc.encode(response_content))
        logger.info(f'总Token数量: {token_num_count}')
        return response_content, token_num_count
        
      except requests.exceptions.Timeout:
        logger.error(f'API请求超时（尝试 #{attempt}） - 超过 {REQUEST_TIMEOUT}秒')
        if attempt == 3:  # 最后一次尝试
          return f'API Timeout - 请求超时，超过{REQUEST_TIMEOUT}秒', token_num_count
        
      except requests.exceptions.ConnectionError as e:
        logger.error(f'API连接错误（尝试 #{attempt}）: {str(e)}')
        if attempt == 3:  # 最后一次尝试
          return f'Connection Error - API连接失败: {str(e)}', token_num_count
          
      except Exception as e:
        error_msg = str(e)
        logger.error(f'API调用失败（尝试 #{attempt}）: {error_msg}')
        
        # 最后一次尝试前等待更长时间
        if attempt < 3:
          wait_time = 20 * attempt  # 第一次20秒，第二次40秒
          logger.info(f'等待 {wait_time} 秒后重试...')
          time.sleep(wait_time)
        else:
          # 最后一次尝试失败
          return f'API Error - {error_msg}', token_num_count

  else:
    raise ValueError(f'Invalid model name: {model_name}')
