# Asynchronous Multi-Agent Framework Implementation

This document describes the asynchronous implementation of the multi-agent framework to prevent API blocking and improve system responsiveness.

## Overview

The async implementation addresses several critical issues in the original synchronous code:

1. **Sequential API Blocking**: Original code made sequential API calls that could block the entire system
2. **Timeout Issues**: No proper timeout handling for API calls
3. **Cascading Failures**: One failed agent could block all subsequent agents
4. **Poor Resource Utilization**: Agents waited idle while others processed

## Key Components

### 1. LLM_async.py

This module provides asynchronous versions of the LLM API calling functions:

- `async_GPT_response()`: Async version of GPT_response with proper timeout handling
- `process_agent_requests()`: Processes multiple agent requests in parallel
- `async_with_action_syntactic_check_func()`: Async version of syntactic checking

**Key Features:**
- Exponential backoff retry strategy (1s, 5s, 15s delays)
- Configurable timeouts (default 30 seconds)
- Parallel processing of multiple agents
- Proper error handling and isolation

### 2. env2_box_arrange_async.py

Async version of the main experiment runner with the following improvements:

**DMAS Framework:**
- All local agents process their prompts in parallel
- No blocking between agent communications
- Faster dialogue history building

**HMAS-2 Framework:**
- Central planner operates asynchronously
- Local agent feedback collection happens in parallel
- Reduced total execution time for multi-round negotiations

**CMAS/HMAS-1 Frameworks:**
- Single central planner with async API calls
- Better timeout handling

### 3. run_async_env2.py

Main execution script that:
- Runs the async experiment framework
- Measures and logs execution times
- Provides proper async event loop management

## Performance Improvements

### Expected Benefits:

1. **Reduced Execution Time**: 
   - DMAS: Up to N times faster (where N = number of agents)
   - HMAS-2: 2-3x faster due to parallel local agent processing

2. **Better Fault Tolerance**:
   - Individual agent failures don't block the entire system
   - Configurable timeouts prevent indefinite hanging

3. **Improved Resource Utilization**:
   - CPU and network resources used more efficiently
   - Better handling of API rate limits

## Usage

### Installation

```bash
pip install -r requirements_async.txt
```

### Running the Async Version

```bash
python run_async_env2.py
```

### Configuration

Key parameters in `run_async_env2.py`:

```python
cen_decen_framework = 'HMAS-2'  # Framework type
local_model_choice = 'gemma-3-27b'  # Local agent model
timeout = 30  # API timeout in seconds
```

## API Timeout Configuration

The async implementation includes several timeout mechanisms:

1. **Individual API Call Timeout**: 30 seconds (configurable)
2. **Retry Delays**: Exponential backoff [1s, 5s, 15s]
3. **Total Retry Timeout**: ~21 seconds maximum per request

## Error Handling

The async implementation provides better error isolation:

- **Agent-Level Errors**: Individual agent failures don't affect others
- **API Timeouts**: Graceful handling with retry mechanisms
- **Network Issues**: Exponential backoff prevents API flooding
- **Model Unavailability**: Proper error messages and fallback handling

## Monitoring and Debugging

Enhanced logging includes:

- Individual agent processing times
- API call success/failure rates
- Timeout occurrences
- Total execution time per experiment

## Comparison with Synchronous Version

| Aspect | Synchronous | Asynchronous |
|--------|-------------|--------------|
| Agent Processing | Sequential | Parallel |
| Blocking Behavior | High risk | Minimal risk |
| Timeout Handling | Basic | Comprehensive |
| Error Isolation | Poor | Excellent |
| Resource Usage | Inefficient | Optimized |
| Execution Time | Longer | Significantly shorter |

## Best Practices

1. **Timeout Configuration**: Adjust timeouts based on your API endpoint performance
2. **Concurrency Limits**: Consider API rate limits when setting parallelism
3. **Error Monitoring**: Monitor logs for patterns in API failures
4. **Resource Management**: Ensure adequate system resources for parallel processing

## Troubleshooting

### Common Issues:

1. **Import Errors**: Ensure all async dependencies are installed
2. **Event Loop Issues**: Use `asyncio.run()` for proper event loop management
3. **API Rate Limiting**: Reduce parallelism if hitting rate limits
4. **Memory Usage**: Monitor memory usage with large numbers of parallel agents

### Debug Mode:

Enable detailed logging by modifying the print statements in the async functions to use proper logging levels.

## Future Enhancements

Potential improvements for the async implementation:

1. **Connection Pooling**: Reuse HTTP connections for better performance
2. **Request Batching**: Batch multiple requests to the same model
3. **Adaptive Timeouts**: Dynamically adjust timeouts based on API performance
4. **Circuit Breaker**: Implement circuit breaker pattern for failing endpoints
5. **Metrics Collection**: Add comprehensive metrics and monitoring

## Migration Guide

To migrate from synchronous to asynchronous version:

1. Install async dependencies: `pip install -r requirements_async.txt`
2. Replace imports: Use `LLM_async` instead of `LLM`
3. Add async/await keywords to function definitions and calls
4. Use `asyncio.run()` for main execution
5. Test with small configurations first

The async implementation maintains backward compatibility with the original data formats and experiment configurations.
