import openai
import tiktoken
import time
import asyncio
import aiohttp
from typing import List, Dict, Tuple, Any, Optional

enc = tiktoken.get_encoding("cl100k_base")
assert enc.decode(enc.encode("hello world")) == "hello world"
enc = tiktoken.encoding_for_model("gpt-4")

# Updated LLM configuration
openai_api_key_name = 'sk-YyiBg6DSn1Fc2KBNU6ZYtw'
openai_base_url = 'http://47.117.124.84:4033/v1'

# Initialize OpenAI clients for different endpoints
custom_client = openai.OpenAI(
    api_key=openai_api_key_name,
    base_url=openai_base_url
)

# For original OpenAI API (if needed)
openai_client = openai.OpenAI(
    api_key=openai_api_key_name
)

# Async client for OpenAI with proper timeout configuration
import httpx

# Create a custom HTTP client with longer timeouts
http_client = httpx.AsyncClient(
    timeout=httpx.Timeout(
        connect=30.0,  # Connection timeout
        read=120.0,    # Read timeout (for model inference)
        write=30.0,    # Write timeout
        pool=30.0      # Pool timeout
    ),
    limits=httpx.Limits(
        max_keepalive_connections=10,
        max_connections=20
    )
)

async_client = openai.AsyncOpenAI(
    api_key=openai_api_key_name,
    base_url=openai_base_url,
    http_client=http_client
)

# Async client for original OpenAI API (if needed)
async_openai_client = openai.AsyncOpenAI(
    api_key=openai_api_key_name
)

def get_model_for_framework(framework, agent_type='central', local_model_choice='gemma-3-27b'):
    """
    Get the appropriate model for a given framework and agent type.

    Args:
        framework: 'CMAS', 'DMAS', 'HMAS-1', 'HMAS-2'
        agent_type: 'central' or 'local' (for hierarchical frameworks)
        local_model_choice: 'gemma-3-27b' or 'qwen3-30b-a3b-mlx' (for DMAS and local agents)

    Returns:
        model_name: The appropriate model name
    """
    # Map user-friendly names to actual model names on the endpoint
    model_mapping = {
        'gemma-3-27b': 'gemma-3-27b-it-qat',
        'qwen3-235b-a22b': 'gemma-3-27b-it-qat',  # Fallback to working model
        'qwen3-30b-a3b-mlx': 'gemma-3-27b-it-qat'  # Fallback to working model
    }

    if framework == 'CMAS':
        # Use gemma-3-27b for central planner (since qwen3-235b-a22b seems unavailable)
        base_model = 'gemma-3-27b'
    elif framework == 'DMAS':
        base_model = local_model_choice
    elif framework in ['HMAS-1', 'HMAS-2']:
        if agent_type == 'central':
            # Use gemma-3-27b for central planner (since qwen3-235b-a22b seems unavailable)
            base_model = 'gemma-3-27b'
        else:  # local agent
            base_model = local_model_choice
    else:
        raise ValueError(f'Invalid framework: {framework}')

    # Return the actual model name used by the endpoint
    return model_mapping.get(base_model, base_model)

# Original synchronous GPT_response function (kept for backward compatibility)
def GPT_response(messages, model_name):
    token_num_count = 0
    for item in messages:
        token_num_count += len(enc.encode(item["content"]))

    # Support both old GPT models and new custom models
    supported_models = [
        'qwen3-235b-a22b', 'gemma-3-27b', 'qwen3-30b-a3b-mlx'
    ]

    if model_name in supported_models:
        # Choose the appropriate client based on model type
        if model_name.startswith('gpt-'):
            # Use original OpenAI API for GPT models
            client = openai_client
        else:
            # Use custom endpoint for new models
            client = custom_client

        try:
            result = client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=0.0,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0,
                timeout=30  # Add explicit timeout
            )
        except Exception as e:
            print(f'First attempt failed: {str(e)}')
            try:
                result = client.chat.completions.create(
                    model=model_name,
                    messages=messages,
                    temperature=0.0,
                    top_p=1,
                    frequency_penalty=0,
                    presence_penalty=0,
                    timeout=30  # Add explicit timeout
                )
            except Exception as e:
                print(f'Second attempt failed: {str(e)}')
                try:
                    print(f'{model_name} Waiting 60 seconds for API query')
                    time.sleep(60)
                    result = client.chat.completions.create(
                        model=model_name,
                        messages=messages,
                        temperature=0.0,
                        top_p=1,
                        frequency_penalty=0,
                        presence_penalty=0,
                        timeout=30  # Add explicit timeout
                    )
                except Exception as e:
                    print(f'Final attempt failed: {str(e)}')
                    return 'Out of tokens', token_num_count

        # Extract response content using the new API structure
        response_content = result.choices[0].message.content
        token_num_count += len(enc.encode(response_content))
        print(f'Token_num_count: {token_num_count}')
        return response_content, token_num_count

    else:
        raise ValueError(f'Invalid model name: {model_name}')

# New asynchronous GPT_response function
async def async_GPT_response(messages, model_name, timeout=120):
    """
    Asynchronous version of GPT_response that doesn't block the main thread.

    Args:
        messages: List of message dictionaries
        model_name: Name of the model to use
        timeout: Timeout in seconds for the API call (increased default to 120s)

    Returns:
        Tuple of (response_content, token_num_count)
    """
    token_num_count = 0
    for item in messages:
        token_num_count += len(enc.encode(item["content"]))

    # Support both old GPT models and new custom models
    supported_models = [
        'qwen3-235b-a22b', 'gemma-3-27b', 'qwen3-30b-a3b-mlx', 'gemma-3-27b-it-qat'
    ]

    if model_name in supported_models:
        # Choose the appropriate client based on model type
        if model_name.startswith('gpt-'):
            # Use original OpenAI API for GPT models
            client = async_openai_client
        else:
            # Use custom endpoint for new models
            client = async_client

        # Implement exponential backoff with longer delays for custom models
        retry_delays = [5, 15, 30]  # Increased delays for model loading
        last_exception = None

        print(f'Making async API call to {model_name} with timeout {timeout}s')

        for retry_attempt, delay in enumerate(retry_delays):
            try:
                print(f'Attempt {retry_attempt+1}/{len(retry_delays)} - Starting API call...')
                start_time = time.time()

                # Use asyncio.wait_for to add timeout to the API call
                result = await asyncio.wait_for(
                    client.chat.completions.create(
                        model=model_name,
                        messages=messages,
                        temperature=0.0,
                        top_p=1,
                        frequency_penalty=0,
                        presence_penalty=0
                    ),
                    timeout=timeout
                )

                elapsed_time = time.time() - start_time
                print(f'API call successful in {elapsed_time:.2f} seconds')

                # Extract response content using the new API structure
                response_content = result.choices[0].message.content
                token_num_count += len(enc.encode(response_content))
                print(f'Token_num_count: {token_num_count}')
                return response_content, token_num_count

            except asyncio.TimeoutError as e:
                elapsed_time = time.time() - start_time
                print(f'Attempt {retry_attempt+1} timed out after {elapsed_time:.2f} seconds (limit: {timeout}s)')
                last_exception = asyncio.TimeoutError(f"API call timed out after {timeout} seconds")
            except asyncio.CancelledError as e:
                print(f'Attempt {retry_attempt+1} was cancelled: {str(e)}')
                last_exception = e
            except Exception as e:
                elapsed_time = time.time() - start_time
                print(f'Attempt {retry_attempt+1} failed after {elapsed_time:.2f}s: {type(e).__name__}: {str(e)}')
                last_exception = e

            if retry_attempt < len(retry_delays) - 1:
                print(f'Waiting {delay} seconds before retry...')
                await asyncio.sleep(delay)

        # If we've exhausted all retries
        print(f'All {len(retry_delays)} attempts failed. Last error: {type(last_exception).__name__}: {str(last_exception)}')
        return 'Out of tokens', token_num_count
    else:
        raise ValueError(f'Invalid model name: {model_name}')

# Function to process multiple agent requests in parallel
async def process_agent_requests(agent_prompts, model_name, timeout=30):
    """
    Process multiple agent requests in parallel.
    
    Args:
        agent_prompts: List of (agent_id, messages) tuples
        model_name: Model name to use for all requests
        timeout: Timeout in seconds for each API call
        
    Returns:
        Dictionary mapping agent_id to (response, token_count)
    """
    tasks = []
    for agent_id, messages in agent_prompts:
        task = asyncio.create_task(async_GPT_response(messages, model_name, timeout))
        tasks.append((agent_id, task))
    
    results = {}
    for agent_id, task in tasks:
        try:
            response, token_count = await task
            results[agent_id] = (response, token_count)
        except Exception as e:
            print(f"Error processing agent {agent_id}: {str(e)}")
            results[agent_id] = ('Error: ' + str(e), 0)
    
    return results

# Async version of syntactic check function
async def async_with_action_syntactic_check_func(pg_dict_input, response, user_prompt_list_input,
                                               response_total_list_input, model_name,
                                               dialogue_history_method, max_iterations=6):
    """
    Asynchronous version of the syntactic check function.
    """
    import json
    import copy
    import re
    from prompt_env2 import message_construct_func

    user_prompt_list = copy.deepcopy(user_prompt_list_input)
    response_total_list = copy.deepcopy(response_total_list_input)
    iteration_num = 0
    token_num_count_list_add = []

    while iteration_num < max_iterations:
        response_total_list.append(response)
        try:
            # Try to parse the response as JSON
            original_response_dict = json.loads(response)

            # Perform validation logic here - this should be adapted from your existing code
            # This is a simplified placeholder
            feedback = ''

            # Add your specific validation logic here based on your requirements
            # For example, checking if the actions are valid, etc.

        except Exception as e:
            feedback = 'Your assigned plan is not in the correct json format. Please reformat your response.'

        if feedback:
            feedback += ' Please replan for all the agents again with the same output format:'
            print('----------Syntactic Check----------')
            print(f'Response original: {response}')
            print(f'Feedback: {feedback}')
            user_prompt_list.append(feedback)

            # Construct messages for the API call
            messages = message_construct_func(user_prompt_list, response_total_list, dialogue_history_method)

            print(f'Length of messages {len(messages)}')
            response, token_num_count = await async_GPT_response(messages, model_name)
            token_num_count_list_add.append(token_num_count)

            print(f'Response new: {response}\n')
            if response == 'Out of tokens':
                return response, token_num_count_list_add

            iteration_num += 1
        else:
            return response, token_num_count_list_add

    return 'Syntactic Error', token_num_count_list_add
