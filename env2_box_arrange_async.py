# Async version of env2-box-arrange.py
from LLM_async import *
from prompt_env2 import *
from env2_create import *
from sre_constants import error
import random
import os
import json
import re
import copy
import numpy as np
import shutil
import time
import asyncio

# cen_decen_framework = 'DMAS', 'HMAS-1', 'CMAS', 'HMAS-2'
# dialogue_history_method = '_w_all_dialogue_history', '_wo_any_dialogue_history', '_w_only_state_action_history'
async def run_exp_async(Saving_path, pg_row_num, pg_column_num, iteration_num, query_time_limit, 
                       dialogue_history_method='_w_all_dialogue_history', cen_decen_framework='CMAS', 
                       local_model_choice='gemma-3-27b'):

    Saving_path_result = Saving_path+f'/env_pg_state_{pg_row_num}_{pg_column_num}/pg_state{iteration_num}/{cen_decen_framework}{dialogue_history_method}'

    # specify the path to your dir for saving the results
    os.makedirs(Saving_path_result, exist_ok=True)
    os.makedirs(Saving_path_result+f'/prompt', exist_ok=True)
    os.makedirs(Saving_path_result+f'/response', exist_ok=True)
    os.makedirs(Saving_path_result+f'/pg_state', exist_ok=True)
    os.makedirs(Saving_path_result + f'/dialogue_history', exist_ok=True)

    with open(Saving_path+f'/env_pg_state_{pg_row_num}_{pg_column_num}/pg_state{iteration_num}/pg_state{iteration_num}.json', 'r') as file:
        pg_dict = json.load(file)

    user_prompt_list = [] # The record list of all the input prompts
    response_total_list = [] # The record list of all the responses
    pg_state_list = [] # The record list of apg states in varied steps
    dialogue_history_list = []
    token_num_count_list = []
    pg_state_list.append(pg_dict)
    with open(Saving_path_result+'/pg_state' + '/pg_state'+str(1)+'.json', 'w') as f:
        json.dump(pg_dict, f)

    ### Start the Game! Query LLM for response
    print(f'query_time_limit: {query_time_limit}')
    for index_query_times in range(query_time_limit): # The upper limit of calling LLMs
        state_update_prompt = state_update_func(pg_row_num, pg_column_num, pg_dict)
        
        if cen_decen_framework in ('DMAS'):
            print('--------DMAS method starts (Async)--------')
            match = None
            count_round = 0
            dialogue_history = ''
            response = '{}'
            
            while not match and count_round <= 3:
                count_round += 1
                
                # Prepare all agent prompts for parallel processing
                agent_prompts = []
                for local_agent_row_i in range(pg_row_num):
                    for local_agent_column_j in range(pg_column_num):
                        state_update_prompt_local_agent, state_update_prompt_other_agent = state_update_func_local_agent(
                            pg_row_num, pg_column_num, local_agent_row_i, local_agent_column_j, pg_dict)
                        
                        user_prompt_1 = input_prompt_local_agent_DMAS_dialogue_func(
                            state_update_prompt_local_agent, state_update_prompt_other_agent,
                            dialogue_history, response_total_list, pg_state_list, 
                            dialogue_history_list, dialogue_history_method)
                        
                        user_prompt_list.append(user_prompt_1)
                        messages = message_construct_func([user_prompt_1], [], '_w_all_dialogue_history')
                        
                        agent_id = f'Agent[{local_agent_row_i+0.5}, {local_agent_column_j+0.5}]'
                        agent_prompts.append((agent_id, messages))
                
                # Process all agents in parallel
                model_name = get_model_for_framework(cen_decen_framework, 'local', local_model_choice)
                agent_results = await process_agent_requests(agent_prompts, model_name, timeout=30)
                
                # Process results
                for agent_id, (initial_response, token_count) in agent_results.items():
                    token_num_count_list.append(token_count)
                    dialogue_history += f'[{agent_id}: {initial_response}]\n\n'
                    
                    if re.search(r'EXECUTE', initial_response):
                        print('EXECUTE!')
                        match = re.search(r'{.*}', initial_response, re.DOTALL)
                        if match:
                            response = match.group()
                            # Use appropriate model for syntactic check in DMAS
                            check_model_name = get_model_for_framework(cen_decen_framework, 'local', local_model_choice)
                            response, token_num_count_list_add = await async_with_action_syntactic_check_func(
                                pg_dict, response, [user_prompt_list[-1]], [], 
                                check_model_name, '_w_all_dialogue_history')
                            token_num_count_list = token_num_count_list + token_num_count_list_add
                            print(f'response: {response}')
                            break
                    
                    if match:
                        break
                        
            dialogue_history_list.append(dialogue_history)
            
        elif cen_decen_framework in ('HMAS-2'):
            print('--------HMAS-2 method starts (Async)--------')
            
            # First, get the central planner response
            user_prompt_1 = input_prompt_1_func_total(state_update_prompt, response_total_list,
                                      pg_state_list, dialogue_history_list,
                                      dialogue_history_method, cen_decen_framework)
            user_prompt_list.append(user_prompt_1)
            messages = message_construct_func([user_prompt_1], [], '_w_all_dialogue_history')
            
            with open(Saving_path_result+'/prompt' + '/user_prompt_'+str(index_query_times+1), 'w') as f:
                f.write(user_prompt_list[-1])
                
            # Use appropriate model for the framework (central planner for HMAS-2)
            model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
            initial_response, token_num_count = await async_GPT_response(messages, model_name)
            print('Initial response: ', initial_response)
            token_num_count_list.append(token_num_count)
            
            response = '{}'  # Initialize response with empty dict
            match = re.search(r'{.*}', initial_response, re.DOTALL)
            if match:
                response = match.group()
                # Use appropriate model for syntactic check
                check_model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
                response, token_num_count_list_add = await async_with_action_syntactic_check_func(
                    pg_dict, response, [user_prompt_1], [], check_model_name, '_w_all_dialogue_history')
                token_num_count_list = token_num_count_list + token_num_count_list_add
            print(f'response: {response}')

            if response == 'Out of tokens':
                success_failure = 'failure over token length limit'
                return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result
            elif response == 'Syntactic Error':
                success_failure = 'Syntactic Error'
                return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result

            # Local agent response for checking the feasibility of actions (HMAS-2 specific)
            break_mark = False
            count_round_HMAS2 = 0

            while break_mark == False and count_round_HMAS2 < 3:
                count_round_HMAS2 += 1
                dialogue_history = f'Central Planner: {response}\n'
                
                # Prepare local agent prompts for parallel processing
                agent_prompts = []
                agent_dict = json.loads(response)
                
                for local_agent_row_i in range(pg_row_num):
                    for local_agent_column_j in range(pg_column_num):
                        agent_key = f'Agent[{local_agent_row_i+0.5}, {local_agent_column_j+0.5}]'
                        if agent_key in agent_dict:
                            state_update_prompt_local_agent, state_update_prompt_other_agent = state_update_func_local_agent(
                                pg_row_num, pg_column_num, local_agent_row_i, local_agent_column_j, pg_dict)

                            local_reprompt = input_prompt_local_agent_HMAS2_dialogue_func(
                                state_update_prompt_local_agent, state_update_prompt_other_agent, 
                                response, response_total_list, pg_state_list, 
                                dialogue_history_list, dialogue_history_method)
                            
                            messages = message_construct_func([local_reprompt], [], '_w_all_dialogue_history')
                            agent_prompts.append((agent_key, messages))
                
                # Process all local agents in parallel
                local_model_name = get_model_for_framework(cen_decen_framework, 'local', local_model_choice)
                local_results = await process_agent_requests(agent_prompts, local_model_name, timeout=30)
                
                # Collect feedback from local agents
                local_agent_response_list_dir = {'feedback1': ''}
                for agent_key, (response_local_agent, token_count) in local_results.items():
                    token_num_count_list.append(token_count)
                    print(f'{agent_key} response: {response_local_agent}')
                    
                    if not ('I Agree' in response_local_agent or 'I agree' in response_local_agent):
                        local_agent_response_list_dir['feedback1'] += f'{agent_key}: {response_local_agent}\n'
                        dialogue_history += f'{agent_key}: {response_local_agent}\n'

                if local_agent_response_list_dir['feedback1'] != '':
                    local_agent_response_list_dir['feedback1'] += '\nThis is the feedback from local agents. If you find some errors in your previous plan, try to modify it. Otherwise, output the same plan as before. The output should have the same json format {Agent[0.5, 0.5]:move(workpiece_blue, position[0.0, 1.0]), Agent[1.5, 0.5]:move...}, as above. Do not explain, just directly output json directory. Your response:'
                    messages = message_construct_func([user_prompt_list[-1], local_agent_response_list_dir['feedback1']], [response], '_w_all_dialogue_history')
                    
                    # Use central planner model for HMAS-2 re-response
                    central_model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
                    response_central_again, token_num_count = await async_GPT_response(messages, central_model_name)
                    token_num_count_list.append(token_num_count)
                    
                    match = re.search(r'{.*}', response_central_again, re.DOTALL)
                    if match:
                        response = match.group()
                        # Use central planner model for syntactic check in HMAS-2
                        check_model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
                        response, token_num_count_list_add = await async_with_action_syntactic_check_func(
                            pg_dict, response_central_again, [user_prompt_list[-1], local_agent_response_list_dir['feedback1']], 
                            [response], check_model_name, '_w_all_dialogue_history')
                        token_num_count_list = token_num_count_list + token_num_count_list_add
                        print(f'Modified plan response: {response}')
                else:
                    break_mark = True

            dialogue_history_list.append(dialogue_history)
            
        else:
            # Handle CMAS and HMAS-1 frameworks (non-async for now, but can be extended)
            user_prompt_1 = input_prompt_1_func_total(state_update_prompt, response_total_list,
                                      pg_state_list, dialogue_history_list,
                                      dialogue_history_method, cen_decen_framework)
            user_prompt_list.append(user_prompt_1)
            messages = message_construct_func([user_prompt_1], [], '_w_all_dialogue_history')

            with open(Saving_path_result+'/prompt' + '/user_prompt_'+str(index_query_times+1), 'w') as f:
                f.write(user_prompt_list[-1])
                
            # Use appropriate model for the framework
            model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
            initial_response, token_num_count = await async_GPT_response(messages, model_name)
            print('Initial response: ', initial_response)
            token_num_count_list.append(token_num_count)
            
            response = '{}'  # Initialize response with empty dict
            match = re.search(r'{.*}', initial_response, re.DOTALL)
            if match:
                response = match.group()
                # Use appropriate model for syntactic check
                check_model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
                response, token_num_count_list_add = await async_with_action_syntactic_check_func(
                    pg_dict, response, [user_prompt_1], [], check_model_name, '_w_all_dialogue_history')
                token_num_count_list = token_num_count_list + token_num_count_list_add
            print(f'response: {response}')

        response_total_list.append(response)
        if response == 'Out of tokens':
            success_failure = 'failure over token length limit'
            return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result
        elif response == 'Syntactic Error':
            success_failure = 'Syntactic Error'
            return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result

        data = json.loads(response)
        
        with open(Saving_path_result+'/response' + '/response'+str(index_query_times+1)+'.json', 'w') as f:
            json.dump(data, f)
        original_response_dict = json.loads(response_total_list[index_query_times])
        print(pg_dict)
        
        if cen_decen_framework in ('DMAS', 'HMAS-1', 'HMAS-1-fast'):
            with open(Saving_path_result+'/dialogue_history' + '/dialogue_history'+str(index_query_times)+'.txt', 'w') as f:
                f.write(dialogue_history_list[index_query_times])
                
        try:
            system_error_feedback, pg_dict_returned, collision_check = action_from_response(pg_dict, original_response_dict)
            if system_error_feedback != '':
                print(system_error_feedback)
            if collision_check:
                print('Collision!')
                success_failure = 'Collision'
                return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result
            pg_dict = pg_dict_returned

        except:
            success_failure = 'Hallucination of wrong plan'
            return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result
            
        pg_state_list.append(pg_dict)
        with open(Saving_path_result+'/pg_state' + '/pg_state'+str(index_query_times+2)+'.json', 'w') as f:
            json.dump(pg_dict, f)

        # Check whether the task has been completed
        count = 0
        for key, value in pg_dict.items():
            count += len(value)
        if count == 0:
            break

    if index_query_times < query_time_limit - 1:
        success_failure = 'success'
    else:
        success_failure = 'failure over query time limit'
    return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result
