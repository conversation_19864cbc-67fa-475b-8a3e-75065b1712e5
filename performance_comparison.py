#!/usr/bin/env python3
"""
Performance comparison between synchronous and asynchronous implementations.
This script compares execution times for different scenarios.
"""

import asyncio
import time
from LLM import GPT_response, get_model_for_framework as sync_get_model
from LLM_async import async_GPT_response, process_agent_requests, get_model_for_framework as async_get_model
from prompt_env2 import message_construct_func

async def benchmark_single_calls(num_calls=5):
    """Benchmark single API calls"""
    print(f"\n=== Benchmarking {num_calls} Single API Calls ===")
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Respond with exactly: 'Task completed successfully.'"}
    ]
    
    model_name = async_get_model('CMAS', 'central', 'gemma-3-27b')
    
    # Test synchronous calls
    print("Testing synchronous calls...")
    sync_start = time.time()
    sync_results = []
    
    for i in range(num_calls):
        try:
            response, tokens = GPT_response(messages, model_name)
            sync_results.append((response, tokens))
        except Exception as e:
            print(f"Sync call {i} failed: {e}")
            sync_results.append(("Error", 0))
    
    sync_end = time.time()
    sync_time = sync_end - sync_start
    
    # Test asynchronous calls (sequential)
    print("Testing asynchronous calls (sequential)...")
    async_seq_start = time.time()
    async_seq_results = []
    
    for i in range(num_calls):
        try:
            response, tokens = await async_GPT_response(messages, model_name, timeout=30)
            async_seq_results.append((response, tokens))
        except Exception as e:
            print(f"Async sequential call {i} failed: {e}")
            async_seq_results.append(("Error", 0))
    
    async_seq_end = time.time()
    async_seq_time = async_seq_end - async_seq_start
    
    # Test asynchronous calls (parallel)
    print("Testing asynchronous calls (parallel)...")
    async_par_start = time.time()
    
    agent_prompts = [(f"Agent_{i}", messages) for i in range(num_calls)]
    async_par_results = await process_agent_requests(agent_prompts, model_name, timeout=30)
    
    async_par_end = time.time()
    async_par_time = async_par_end - async_par_start
    
    # Results
    print(f"\nResults for {num_calls} API calls:")
    print(f"  Synchronous:           {sync_time:.2f} seconds")
    print(f"  Async (sequential):    {async_seq_time:.2f} seconds")
    print(f"  Async (parallel):      {async_par_time:.2f} seconds")
    print(f"  Speedup (parallel):    {sync_time/async_par_time:.2f}x")
    
    return {
        'sync_time': sync_time,
        'async_seq_time': async_seq_time,
        'async_par_time': async_par_time,
        'speedup': sync_time/async_par_time if async_par_time > 0 else 0
    }

async def benchmark_agent_scenarios():
    """Benchmark different agent scenarios"""
    print(f"\n=== Benchmarking Agent Scenarios ===")
    
    scenarios = [
        ("2x2 Grid (4 agents)", 4),
        ("2x4 Grid (8 agents)", 8),
        ("4x4 Grid (16 agents)", 16),
    ]
    
    results = {}
    
    for scenario_name, num_agents in scenarios:
        print(f"\nTesting {scenario_name}...")
        
        # Create agent prompts
        agent_prompts = []
        for i in range(num_agents):
            messages = [
                {"role": "system", "content": "You are a robotic agent in a multi-agent system."},
                {"role": "user", "content": f"Agent {i}: Report your status in one sentence."}
            ]
            agent_prompts.append((f"Agent_{i}", messages))
        
        model_name = async_get_model('DMAS', 'local', 'gemma-3-27b')
        
        # Test parallel processing
        start_time = time.time()
        parallel_results = await process_agent_requests(agent_prompts, model_name, timeout=30)
        end_time = time.time()
        
        parallel_time = end_time - start_time
        successful_agents = len([r for r in parallel_results.values() if not r[0].startswith('Error')])
        
        print(f"  Parallel time: {parallel_time:.2f} seconds")
        print(f"  Successful agents: {successful_agents}/{num_agents}")
        print(f"  Average time per agent: {parallel_time/num_agents:.2f} seconds")
        
        # Estimate sequential time (parallel_time * num_agents)
        estimated_sequential = parallel_time * num_agents
        estimated_speedup = estimated_sequential / parallel_time if parallel_time > 0 else 0
        
        print(f"  Estimated sequential time: {estimated_sequential:.2f} seconds")
        print(f"  Estimated speedup: {estimated_speedup:.2f}x")
        
        results[scenario_name] = {
            'num_agents': num_agents,
            'parallel_time': parallel_time,
            'estimated_sequential': estimated_sequential,
            'speedup': estimated_speedup,
            'success_rate': successful_agents / num_agents
        }
    
    return results

async def benchmark_timeout_recovery():
    """Benchmark timeout and recovery mechanisms"""
    print(f"\n=== Benchmarking Timeout Recovery ===")
    
    # Test with very short timeout to trigger recovery
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Write a detailed explanation of quantum computing."}
    ]
    
    model_name = async_get_model('CMAS', 'central', 'gemma-3-27b')
    
    print("Testing timeout recovery with 2-second timeout...")
    start_time = time.time()
    
    try:
        response, tokens = await async_GPT_response(messages, model_name, timeout=2)
        end_time = time.time()
        
        if response == 'Out of tokens':
            print(f"✓ Timeout handled correctly in {end_time - start_time:.2f} seconds")
        else:
            print(f"✓ Request completed successfully in {end_time - start_time:.2f} seconds")
            print(f"  Response length: {len(response)} characters")
    except Exception as e:
        end_time = time.time()
        print(f"✗ Timeout test failed: {e} (took {end_time - start_time:.2f} seconds)")

def print_summary(single_results, agent_results):
    """Print performance summary"""
    print(f"\n" + "="*60)
    print("PERFORMANCE SUMMARY")
    print("="*60)
    
    print(f"\nSingle API Call Performance:")
    print(f"  Best speedup achieved: {single_results['speedup']:.2f}x")
    print(f"  Parallel execution time: {single_results['async_par_time']:.2f}s")
    
    print(f"\nMulti-Agent Scenarios:")
    for scenario, data in agent_results.items():
        print(f"  {scenario}:")
        print(f"    Agents: {data['num_agents']}")
        print(f"    Parallel time: {data['parallel_time']:.2f}s")
        print(f"    Estimated speedup: {data['speedup']:.2f}x")
        print(f"    Success rate: {data['success_rate']*100:.1f}%")
    
    print(f"\nKey Benefits of Async Implementation:")
    print(f"  ✓ Parallel processing of multiple agents")
    print(f"  ✓ Better timeout handling and recovery")
    print(f"  ✓ Improved fault isolation")
    print(f"  ✓ Reduced total execution time")
    print(f"  ✓ Better resource utilization")

async def main():
    """Run performance benchmarks"""
    print("🚀 Multi-Agent Framework Performance Comparison")
    print("=" * 60)
    
    try:
        # Run benchmarks
        single_results = await benchmark_single_calls(num_calls=3)
        agent_results = await benchmark_agent_scenarios()
        await benchmark_timeout_recovery()
        
        # Print summary
        print_summary(single_results, agent_results)
        
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        print("Note: This might be due to API endpoint availability or network issues.")

if __name__ == "__main__":
    asyncio.run(main())
