#!/usr/bin/env python3
"""
Test script for the async LLM implementation.
This script tests the basic functionality without running full experiments.
"""

import asyncio
import time
from LLM_async import async_GPT_response, process_agent_requests, get_model_for_framework

async def test_single_async_call():
    """Test a single async API call"""
    print("Testing single async API call...")
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Say 'Hello, this is a test!' and nothing else."}
    ]
    
    model_name = get_model_for_framework('CMAS', 'central', 'gemma-3-27b')
    
    start_time = time.time()
    try:
        response, token_count = await async_GPT_response(messages, model_name, timeout=10)
        end_time = time.time()
        
        print(f"✓ Single call successful!")
        print(f"  Response: {response[:50]}...")
        print(f"  Token count: {token_count}")
        print(f"  Time taken: {end_time - start_time:.2f} seconds")
        return True
    except Exception as e:
        print(f"✗ Single call failed: {str(e)}")
        return False

async def test_parallel_calls():
    """Test parallel API calls"""
    print("\nTesting parallel API calls...")
    
    # Create multiple agent prompts
    agent_prompts = []
    for i in range(3):
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": f"Say 'Hello from agent {i}!' and nothing else."}
        ]
        agent_prompts.append((f"Agent_{i}", messages))
    
    model_name = get_model_for_framework('DMAS', 'local', 'gemma-3-27b')
    
    start_time = time.time()
    try:
        results = await process_agent_requests(agent_prompts, model_name, timeout=10)
        end_time = time.time()
        
        print(f"✓ Parallel calls successful!")
        print(f"  Number of agents: {len(results)}")
        for agent_id, (response, token_count) in results.items():
            print(f"  {agent_id}: {response[:30]}... (tokens: {token_count})")
        print(f"  Total time: {end_time - start_time:.2f} seconds")
        return True
    except Exception as e:
        print(f"✗ Parallel calls failed: {str(e)}")
        return False

async def test_timeout_handling():
    """Test timeout handling"""
    print("\nTesting timeout handling...")
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Write a very long story about a robot."}
    ]
    
    model_name = get_model_for_framework('CMAS', 'central', 'gemma-3-27b')
    
    start_time = time.time()
    try:
        # Use a very short timeout to test timeout handling
        response, token_count = await async_GPT_response(messages, model_name, timeout=1)
        end_time = time.time()
        
        if response == 'Out of tokens':
            print(f"✓ Timeout handling working correctly!")
            print(f"  Time taken: {end_time - start_time:.2f} seconds")
            return True
        else:
            print(f"✓ Call completed within timeout")
            print(f"  Response: {response[:50]}...")
            print(f"  Time taken: {end_time - start_time:.2f} seconds")
            return True
    except Exception as e:
        print(f"✗ Timeout test failed: {str(e)}")
        return False

async def test_model_selection():
    """Test model selection for different frameworks"""
    print("\nTesting model selection...")
    
    test_cases = [
        ('CMAS', 'central', 'gemma-3-27b'),
        ('DMAS', 'local', 'gemma-3-27b'),
        ('HMAS-1', 'central', 'gemma-3-27b'),
        ('HMAS-1', 'local', 'gemma-3-27b'),
        ('HMAS-2', 'central', 'qwen3-30b-a3b-mlx'),
        ('HMAS-2', 'local', 'qwen3-30b-a3b-mlx'),
    ]
    
    print("✓ Model selection test:")
    for framework, agent_type, local_choice in test_cases:
        model = get_model_for_framework(framework, agent_type, local_choice)
        print(f"  {framework} {agent_type}: {model}")
    
    return True

async def main():
    """Run all tests"""
    print("=== Async LLM Implementation Test Suite ===\n")
    
    tests = [
        test_model_selection,
        test_single_async_call,
        test_parallel_calls,
        test_timeout_handling,
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {str(e)}")
            results.append(False)
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 All tests passed! The async implementation is ready to use.")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
        print("Note: API failures might be due to network issues or endpoint availability.")

if __name__ == "__main__":
    asyncio.run(main())
